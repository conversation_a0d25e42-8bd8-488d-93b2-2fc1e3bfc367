// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Entities;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository interface for framework configuration data access
/// </summary>
[Testable("Repository", IncludePerformanceTests = true, ExpectedExecutionTimeMs = 50)]
public interface IConfigurationRepository : IBaseRepository<FrameworkConfiguration>
{
    /// <summary>
    /// Gets configuration by key
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration if found, null otherwise</returns>
    Task<FrameworkConfiguration?> GetByKeyAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configuration value by key
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration value if found, null otherwise</returns>
    Task<string?> GetValueAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configuration value by key with default fallback
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="defaultValue">Default value if not found</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration value or default value</returns>
    Task<string> GetValueOrDefaultAsync(string key, string defaultValue, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations by category
    /// </summary>
    /// <param name="category">Category name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations by scope
    /// </summary>
    /// <param name="scope">Scope name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetByScopeAsync(string scope, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations by environment
    /// </summary>
    /// <param name="environment">Environment name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations by module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetByModuleAsync(string moduleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations by user
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetByUserAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets feature flags
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of feature flag configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetFeatureFlagsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of active configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetActiveConfigurationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets environment-specific configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of environment-specific configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetEnvironmentSpecificConfigurationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user-specific configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user-specific configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetUserSpecificConfigurationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configurations that require restart
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of configurations requiring restart</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetConfigurationsRequiringRestartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets read-only configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of read-only configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetReadOnlyConfigurationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets encrypted configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of encrypted configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetEncryptedConfigurationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets configuration value
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="value">Configuration value</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated configuration</returns>
    Task<FrameworkConfiguration> SetValueAsync(string key, string value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets multiple configuration values
    /// </summary>
    /// <param name="configurations">Dictionary of key-value pairs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task SetValuesAsync(Dictionary<string, string> configurations, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates configuration value against constraints
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="value">Value to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ConfigurationValidationResult> ValidateValueAsync(string key, string value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets configuration access statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration access statistics</returns>
    Task<ConfigurationStatistics> GetAccessStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets most accessed configurations
    /// </summary>
    /// <param name="count">Number of configurations to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of most accessed configurations</returns>
    Task<IEnumerable<FrameworkConfiguration>> GetMostAccessedConfigurationsAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates configuration access count
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateAccessCountAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Exports configurations to dictionary
    /// </summary>
    /// <param name="scope">Optional scope filter</param>
    /// <param name="environment">Optional environment filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of configuration key-value pairs</returns>
    Task<Dictionary<string, string>> ExportConfigurationsAsync(string? scope = null, string? environment = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Imports configurations from dictionary
    /// </summary>
    /// <param name="configurations">Dictionary of configuration key-value pairs</param>
    /// <param name="overwriteExisting">Whether to overwrite existing configurations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of imported configurations</returns>
    Task<int> ImportConfigurationsAsync(Dictionary<string, string> configurations, bool overwriteExisting = false, CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration validation result
/// </summary>
public class ConfigurationValidationResult
{
    /// <summary>
    /// Gets or sets whether the validation passed
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Gets or sets the validation error message
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the validated value
    /// </summary>
    public string? ValidatedValue { get; set; }

    /// <summary>
    /// Gets or sets validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Configuration access statistics
/// </summary>
public class ConfigurationStatistics
{
    /// <summary>
    /// Gets or sets the total number of configurations
    /// </summary>
    public int TotalConfigurations { get; set; }

    /// <summary>
    /// Gets or sets the number of active configurations
    /// </summary>
    public int ActiveConfigurations { get; set; }

    /// <summary>
    /// Gets or sets the number of feature flags
    /// </summary>
    public int FeatureFlags { get; set; }

    /// <summary>
    /// Gets or sets the number of environment-specific configurations
    /// </summary>
    public int EnvironmentSpecificConfigurations { get; set; }

    /// <summary>
    /// Gets or sets the number of user-specific configurations
    /// </summary>
    public int UserSpecificConfigurations { get; set; }

    /// <summary>
    /// Gets or sets the total access count across all configurations
    /// </summary>
    public long TotalAccessCount { get; set; }

    /// <summary>
    /// Gets or sets the average access count per configuration
    /// </summary>
    public double AverageAccessCount { get; set; }

    /// <summary>
    /// Gets or sets the most accessed configuration key
    /// </summary>
    public string? MostAccessedKey { get; set; }

    /// <summary>
    /// Gets or sets the access count of the most accessed configuration
    /// </summary>
    public long MostAccessedCount { get; set; }
}
