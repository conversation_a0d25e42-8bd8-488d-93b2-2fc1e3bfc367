// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Entities;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository interface for module health status data access
/// </summary>
[Testable("Repository", IncludePerformanceTests = true, ExpectedExecutionTimeMs = 50)]
public interface IModuleHealthRepository : IBaseRepository<ModuleHealthStatus>
{
    /// <summary>
    /// Gets module health status by module name
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of module health statuses</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetByModuleAsync(string moduleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the latest health status for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Latest module health status</returns>
    Task<ModuleHealthStatus?> GetLatestByModuleAsync(string moduleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health statuses by current status
    /// </summary>
    /// <param name="status">Current status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of module health statuses</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health statuses by session ID
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of module health statuses</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health statuses within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of module health statuses</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health statuses by environment
    /// </summary>
    /// <param name="environment">Environment name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of module health statuses</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules with health score below threshold
    /// </summary>
    /// <param name="threshold">Health score threshold (0.0 to 1.0)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of unhealthy modules</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetUnhealthyModulesAsync(double threshold = 0.8, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules with build failures
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of modules with build failures</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetModulesWithBuildFailuresAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules with failing tests
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of modules with failing tests</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetModulesWithFailingTestsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules with phantom implementations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of modules with phantom implementations</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetModulesWithPhantomImplementationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules with critical issues
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of modules with critical issues</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetModulesWithCriticalIssuesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Module health statistics</returns>
    Task<ModuleHealthStatistics> GetHealthStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets health trend data for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="days">Number of days to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of health trend data</returns>
    Task<IEnumerable<HealthTrendData>> GetHealthTrendAsync(string moduleName, int days = 30, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets modules that need validation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of modules needing validation</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetModulesNeedingValidationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets automated validation results
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of automated validation results</returns>
    Task<IEnumerable<ModuleHealthStatus>> GetAutomatedValidationResultsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets module health comparison between two dates
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="date1">First date</param>
    /// <param name="date2">Second date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health comparison data</returns>
    Task<HealthComparisonData?> GetHealthComparisonAsync(string moduleName, DateTime date1, DateTime date2, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes old health statuses based on retention policy
    /// </summary>
    /// <param name="retentionDays">Number of days to retain</param>
    /// <param name="keepLatestPerModule">Number of latest records to keep per module</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted records</returns>
    Task<int> CleanupOldHealthStatusesAsync(int retentionDays, int keepLatestPerModule = 5, CancellationToken cancellationToken = default);
}

/// <summary>
/// Module health statistics
/// </summary>
public class ModuleHealthStatistics
{
    /// <summary>
    /// Gets or sets the total number of modules
    /// </summary>
    public int TotalModules { get; set; }

    /// <summary>
    /// Gets or sets the number of healthy modules
    /// </summary>
    public int HealthyModules { get; set; }

    /// <summary>
    /// Gets or sets the number of unhealthy modules
    /// </summary>
    public int UnhealthyModules { get; set; }

    /// <summary>
    /// Gets or sets the average health score
    /// </summary>
    public double AverageHealthScore { get; set; }

    /// <summary>
    /// Gets or sets the number of modules with build failures
    /// </summary>
    public int ModulesWithBuildFailures { get; set; }

    /// <summary>
    /// Gets or sets the number of modules with failing tests
    /// </summary>
    public int ModulesWithFailingTests { get; set; }

    /// <summary>
    /// Gets or sets the number of modules with phantom implementations
    /// </summary>
    public int ModulesWithPhantomImplementations { get; set; }

    /// <summary>
    /// Gets or sets the number of modules with critical issues
    /// </summary>
    public int ModulesWithCriticalIssues { get; set; }

    /// <summary>
    /// Gets the percentage of healthy modules
    /// </summary>
    public double HealthyPercentage => TotalModules > 0 ? (double)HealthyModules / TotalModules * 100 : 0;

    /// <summary>
    /// Gets the percentage of modules with build failures
    /// </summary>
    public double BuildFailurePercentage => TotalModules > 0 ? (double)ModulesWithBuildFailures / TotalModules * 100 : 0;
}

/// <summary>
/// Health trend data point
/// </summary>
public class HealthTrendData
{
    /// <summary>
    /// Gets or sets the date of the data point
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Gets or sets the health score for the date
    /// </summary>
    public double HealthScore { get; set; }

    /// <summary>
    /// Gets or sets the number of passing tests
    /// </summary>
    public int PassingTests { get; set; }

    /// <summary>
    /// Gets or sets the number of failing tests
    /// </summary>
    public int FailingTests { get; set; }

    /// <summary>
    /// Gets or sets whether the module built successfully
    /// </summary>
    public bool BuildSuccessful { get; set; }

    /// <summary>
    /// Gets or sets the number of critical issues
    /// </summary>
    public int CriticalIssues { get; set; }
}

/// <summary>
/// Health comparison data between two time points
/// </summary>
public class HealthComparisonData
{
    /// <summary>
    /// Gets or sets the module name
    /// </summary>
    public string ModuleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the first date health score
    /// </summary>
    public double HealthScore1 { get; set; }

    /// <summary>
    /// Gets or sets the second date health score
    /// </summary>
    public double HealthScore2 { get; set; }

    /// <summary>
    /// Gets the health score change
    /// </summary>
    public double HealthScoreChange => HealthScore2 - HealthScore1;

    /// <summary>
    /// Gets or sets the first date test count
    /// </summary>
    public int TestCount1 { get; set; }

    /// <summary>
    /// Gets or sets the second date test count
    /// </summary>
    public int TestCount2 { get; set; }

    /// <summary>
    /// Gets the test count change
    /// </summary>
    public int TestCountChange => TestCount2 - TestCount1;

    /// <summary>
    /// Gets or sets whether health improved
    /// </summary>
    public bool HealthImproved => HealthScoreChange > 0;
}
