// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository implementation for performance benchmark data access
/// </summary>
[Testable("Repository", IncludePerformanceTests = true, ExpectedExecutionTimeMs = 50)]
public class PerformanceRepository : BaseRepository<PerformanceBenchmark>, IPerformanceRepository
{
    /// <summary>
    /// Initializes a new instance of the PerformanceRepository class
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger instance</param>
    public PerformanceRepository(ArtDesignFrameworkDbContext context, ILogger<PerformanceRepository> logger)
        : base(context, logger)
    {
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetByOperationAsync(string operationName, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.OperationName == operationName)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.Category == category)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.SessionId == sessionId)
            .OrderBy(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.Environment == environment)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetByTargetStatusAsync(bool metTarget, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.MetTarget == metTarget)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetSlowBenchmarksAsync(double thresholdMs, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.ExecutionTimeMs > thresholdMs)
            .OrderByDescending(p => p.ExecutionTimeMs)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetMemoryIntensiveBenchmarksAsync(long thresholdBytes, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.MemoryUsageBytes > thresholdBytes)
            .OrderByDescending(p => p.MemoryUsageBytes)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<PerformanceStatistics> GetOperationStatisticsAsync(string operationName, CancellationToken cancellationToken = default)
    {
        var benchmarks = await DbSet.Where(p => p.OperationName == operationName).ToListAsync(cancellationToken);

        if (!benchmarks.Any())
            return new PerformanceStatistics();

        var executionTimes = benchmarks.Select(b => b.ExecutionTimeMs).OrderBy(t => t).ToList();

        return new PerformanceStatistics
        {
            TotalBenchmarks = benchmarks.Count,
            AverageExecutionTimeMs = benchmarks.Average(b => b.ExecutionTimeMs),
            MinExecutionTimeMs = benchmarks.Min(b => b.ExecutionTimeMs),
            MaxExecutionTimeMs = benchmarks.Max(b => b.ExecutionTimeMs),
            MedianExecutionTimeMs = GetMedian(executionTimes),
            P95ExecutionTimeMs = GetPercentile(executionTimes, 0.95),
            AverageMemoryUsageBytes = (long)benchmarks.Average(b => b.MemoryUsageBytes),
            PeakMemoryUsageBytes = benchmarks.Max(b => b.PeakMemoryUsageBytes),
            AverageThroughputOps = benchmarks.Where(b => b.ThroughputOps.HasValue).Average(b => b.ThroughputOps!.Value),
            BenchmarksMetTarget = benchmarks.Count(b => b.MetTarget == true)
        };
    }

    /// <inheritdoc />
    public async Task<PerformanceStatistics> GetCategoryStatisticsAsync(string category, CancellationToken cancellationToken = default)
    {
        var benchmarks = await DbSet.Where(p => p.Category == category).ToListAsync(cancellationToken);

        if (!benchmarks.Any())
            return new PerformanceStatistics();

        var executionTimes = benchmarks.Select(b => b.ExecutionTimeMs).OrderBy(t => t).ToList();

        return new PerformanceStatistics
        {
            TotalBenchmarks = benchmarks.Count,
            AverageExecutionTimeMs = benchmarks.Average(b => b.ExecutionTimeMs),
            MinExecutionTimeMs = benchmarks.Min(b => b.ExecutionTimeMs),
            MaxExecutionTimeMs = benchmarks.Max(b => b.ExecutionTimeMs),
            MedianExecutionTimeMs = GetMedian(executionTimes),
            P95ExecutionTimeMs = GetPercentile(executionTimes, 0.95),
            AverageMemoryUsageBytes = (long)benchmarks.Average(b => b.MemoryUsageBytes),
            PeakMemoryUsageBytes = benchmarks.Max(b => b.PeakMemoryUsageBytes),
            AverageThroughputOps = benchmarks.Where(b => b.ThroughputOps.HasValue).Average(b => b.ThroughputOps!.Value),
            BenchmarksMetTarget = benchmarks.Count(b => b.MetTarget == true)
        };
    }

    /// <inheritdoc />
    public async Task<PerformanceStatistics> GetStatisticsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var benchmarks = await DbSet.Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate).ToListAsync(cancellationToken);

        if (!benchmarks.Any())
            return new PerformanceStatistics();

        var executionTimes = benchmarks.Select(b => b.ExecutionTimeMs).OrderBy(t => t).ToList();

        return new PerformanceStatistics
        {
            TotalBenchmarks = benchmarks.Count,
            AverageExecutionTimeMs = benchmarks.Average(b => b.ExecutionTimeMs),
            MinExecutionTimeMs = benchmarks.Min(b => b.ExecutionTimeMs),
            MaxExecutionTimeMs = benchmarks.Max(b => b.ExecutionTimeMs),
            MedianExecutionTimeMs = GetMedian(executionTimes),
            P95ExecutionTimeMs = GetPercentile(executionTimes, 0.95),
            AverageMemoryUsageBytes = (long)benchmarks.Average(b => b.MemoryUsageBytes),
            PeakMemoryUsageBytes = benchmarks.Max(b => b.PeakMemoryUsageBytes),
            AverageThroughputOps = benchmarks.Where(b => b.ThroughputOps.HasValue).Average(b => b.ThroughputOps!.Value),
            BenchmarksMetTarget = benchmarks.Count(b => b.MetTarget == true)
        };
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetRecentBenchmarksAsync(string operationName, int count = 10, CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.OperationName == operationName)
            .OrderByDescending(p => p.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceTrendData>> GetPerformanceTrendAsync(string operationName, int days = 30, CancellationToken cancellationToken = default)
    {
        var startDate = DateTime.UtcNow.AddDays(-days);
        var benchmarks = await DbSet.Where(p => p.OperationName == operationName && p.CreatedAt >= startDate)
            .ToListAsync(cancellationToken);

        return benchmarks
            .GroupBy(b => b.CreatedAt.Date)
            .Select(g => new PerformanceTrendData
            {
                Date = g.Key,
                AverageExecutionTimeMs = g.Average(b => b.ExecutionTimeMs),
                AverageMemoryUsageBytes = (long)g.Average(b => b.MemoryUsageBytes),
                BenchmarkCount = g.Count(),
                ImprovementPercentage = g.Average(b => b.ActualImprovement)
            })
            .OrderBy(t => t.Date);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetRegressionBenchmarksAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.IsRegressionBenchmark)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PerformanceBenchmark>> GetAutomatedBenchmarksAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(p => p.IsAutomatedBenchmark)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<int> CleanupOldBenchmarksAsync(int retentionDays, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
        var oldBenchmarks = await DbSet.Where(p => p.CreatedAt < cutoffDate).ToListAsync(cancellationToken);

        if (oldBenchmarks.Any())
        {
            DbSet.RemoveRange(oldBenchmarks);
            await Context.SaveChangesAsync(cancellationToken);
        }

        return oldBenchmarks.Count;
    }

    private static double GetMedian(List<double> values)
    {
        if (!values.Any()) return 0;

        var count = values.Count;
        return count % 2 == 0
            ? (values[count / 2 - 1] + values[count / 2]) / 2.0
            : values[count / 2];
    }

    private static double GetPercentile(List<double> values, double percentile)
    {
        if (!values.Any()) return 0;

        var index = (int)Math.Ceiling(values.Count * percentile) - 1;
        return values[Math.Max(0, Math.Min(index, values.Count - 1))];
    }
}
